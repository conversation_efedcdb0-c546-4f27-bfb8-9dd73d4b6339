import java.util.regex.*;
public class RegexExample {
    public static void main(String[] args) {
        String regex = "^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\\.[a-zA-Z]{2,}$ ";  // Matches exactly 3 digits 
        Pattern pattern = Pattern.compile(regex);
        // Input string to check 
        String input = "<EMAIL>";
        // Create matcher object
        Matcher matcher = pattern.matcher(input);
        // Find matches
        while (matcher.find()) {
            System.out.println("Matched: " + matcher.group()); 
        }
    }
}