package com.DSA.Hash;
import java.util.*;

public class TwoSum {
    public static int[] findTwoSum(int[] nums, int target) {
        Map<Integer, Integer> map = new HashMap<>();

        for (int i = 0; i < nums.length; i++) {
            int remain = target - nums[i];
            if (map.containsKey(remain))
                return new int[]{map.get(remain), i};
            map.put(nums[i], i);
        }

        return new int[]{-1, -1}; // not found
    }

    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        System.out.print("Enter array size: ");
        int n = sc.nextInt();
        int[] nums = new int[n];
        System.out.println("Enter array elements:");
        for (int i = 0; i < n; i++)
            nums[i] = sc.nextInt();
        System.out.print("Enter target sum: ");
        int target = sc.nextInt();
        int[] result = findTwoSum(nums, target);
        if (result[0] == -1)
            System.out.println("No pair found.");
        else
            System.out.println("Indices: " + result[0] + " and " + result[1]);
        sc.close();
    }
}

