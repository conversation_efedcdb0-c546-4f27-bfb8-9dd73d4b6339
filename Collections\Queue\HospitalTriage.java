package com.java.Collections.Queue;
import java.util.*;

class Patient {
    String name;
    int severity;

    public Patient(String name, int severity) {
        this.name = name;
        this.severity = severity;
    }

    @Override
    public String toString() {
        return name;
    }
}

public class HospitalTriage {
    public static void main(String[] args) {
        PriorityQueue<Patient> pq = new PriorityQueue<>(
            (a, b) -> b.severity - a.severity // Higher severity first
        );

        pq.add(new Patient("<PERSON>", 3));
        pq.add(new Patient("<PERSON>", 5));
        pq.add(new Patient("<PERSON>", 2));

        while (!pq.isEmpty()) {
            System.out.println(pq.remove());
        }
    }
}